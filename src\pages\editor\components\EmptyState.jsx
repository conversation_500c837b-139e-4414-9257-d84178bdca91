import React, { useState } from 'react';
import { Plus, BookTemplate } from 'lucide-react';
import Modal from '@/components/ui/Modal';
import TemplateCard from "@/components/ui/TemplateCard";
import EbookCardSkeleton from "@/components/ui/EbookCardSkeleton";
import useDataFetching from "@/hooks/useDataFetching";
import api from "@/lib/axios";
import { useQueryClient } from '@tanstack/react-query';
import Confirm from "@/components/ui/Confirm";

const EmptyState = ({ handleAddPage, bookId }) => {
  const [isTemplateModalOpen, setIsTemplateModalOpen] = useState(false);
  const queryClient = useQueryClient();

  const { data: templates, isLoading } = useDataFetching({
    queryKey: ["templates"],
    endPoint: "/admin/templates",
  });

  const handleUseTemplate = (template) => {
    Confirm(
      async () => {
        try {
          await api.post(`admin/ebooks/use-templates`, { 
            ebook_id: bookId, 
            template_id: template.id 
          });
          queryClient.invalidateQueries('ebooksData');
          setIsTemplateModalOpen(false);
        } catch (error) {
          console.error('Error using template:', error);
        }
      },
      "Are you sure you want to use this template?",
      "This template",
      "will replace your current book page data.",
      "Cancel",
      "Use Template"
    );
  };

  return (
    <>
      <div className="flex items-center justify-center mt-20 bg-white h-[500px] w-[500px] rounded-lg shadow-lg">
        <div className="text-center space-y-6">
          <h3 className="text-2xl font-semibold text-gray-700">Start Creating Your Book</h3>
          <p className="text-gray-500">Choose how you want to begin</p>
          <div className="flex gap-4 justify-center">
            <button
              className="flex items-center gap-2 bg-blue-500 hover:bg-blue-600 text-white font-semibold py-3 px-6 rounded-lg transition duration-200 shadow-md hover:shadow-lg"
              onClick={handleAddPage}
            >
              <Plus size={20} />
              Start from Scratch
            </button>
            <button
              className="flex items-center gap-2 bg-purple-500 hover:bg-purple-600 text-white font-semibold py-3 px-6 rounded-lg transition duration-200 shadow-md hover:shadow-lg"
              onClick={() => setIsTemplateModalOpen(true)}
            >
              <BookTemplate size={20} />
              Choose Template
            </button>
          </div>
        </div>
      </div>

      <Modal
        activeModal={isTemplateModalOpen}
        onClose={() => setIsTemplateModalOpen(false)}
        title="Choose a Template"
        className="w-full max-w-5xl"
      >
        <div className="p-6">
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
            {isLoading ? (
              Array.from({ length: 6 }, (_, index) => (
                <EbookCardSkeleton key={index} />
              ))
            ) : templates?.data?.data?.length > 0 ? (
              templates.data.data.map((template) => (
                <TemplateCard
                  key={template.id}
                  book={template}
                  onUse={() => handleUseTemplate(template)}
                />
              ))
            ) : (
              <div className="col-span-full text-center text-gray-500 py-8">
                No templates available
              </div>
            )}
          </div>
        </div>
      </Modal>
    </>
  );
};

export default EmptyState;