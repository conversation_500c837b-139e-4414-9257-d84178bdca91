import React, { useEffect, useRef, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import HtmlContent from "./components/HtmlContent";
import Loading from "@/components/Loading";
import Toolbar from "./components/Toolbar";
import BookToolbar from "./sections/BookToolbar";
import { useZoomableScale } from "@/hooks/useScale";
import JumpToPage from "./components/JumpToPage";
import useDataFetching from "../../hooks/useDataFetching";

export default function BookScrollView() {
  const [clickedPageId, setClickedPageId] = useState(null);
  const { slug, id } = useParams();
  const { scale, zoomIn, zoomOut, resetZoom } = useZoomableScale(0.3, 1, 900);
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);
  const navigate = useNavigate();
  const pageRefs = useRef([]);
  const [activePage, setActivePage] = useState(null);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const endPoint = `/admin/templates/${id}`;
  const { data, isLoading, error } = useDataFetching({
    queryKey: ["templates", id],
    endPoint,
  });
  
  const ebook = data?.data;

  // Page observer logic
  useEffect(() => {
    if (!ebook?.pages) return;

    const observerOptions = {
      root: null,
      rootMargin: "0px",
      threshold: 0.6,
    };

    const observerCallback = (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const idx = pageRefs.current.indexOf(entry.target);
          if (idx !== -1) setActivePage(idx);
        }
      });
    };

    const observer = new IntersectionObserver(observerCallback, observerOptions);
    pageRefs.current.forEach((pageEl) => {
      if (pageEl) observer.observe(pageEl);
    });

    return () => observer.disconnect();
  }, [ebook]);

  useEffect(() => {
    if (error) {
      navigate("/404");
    }
  }, [error, navigate]);

  if (isLoading) {
    return <Loading />;
  }

  const handleJumpToPage = (pageIndex) => {
    if (pageRefs.current[pageIndex]) {
      pageRefs.current[pageIndex].scrollIntoView({
        behavior: "smooth",
        block: "start",
      });
      setActivePage(pageIndex);
    }
  };

  return (
    <div className="relative h-screen">
      <Toolbar clickedPageId={clickedPageId} />
      <div className="fixed top-3 left-1/2 transform -translate-x-1/2 z-50 hidden md:block">
        <JumpToPage
          totalPages={ebook?.pages?.length}
          onJump={handleJumpToPage}
        />
      </div>

      <div
        className={`fixed bg-gray-50 border ${
          isMobile
            ? "bottom-0 z-50 w-full flex flex-row justify-center py-2 border-t"
            : "left-0 top-10 h-full flex flex-col justify-center border-r"
        }`}
      >
        <BookToolbar
          zoomIn={zoomIn}
          zoomOut={zoomOut}
          resetZoom={resetZoom}
        />
      </div>

      <div className="flex flex-col w-full" key={scale}>
        <div className="flex flex-col items-center mt-20 gap-8 w-full px-4 md:px-8">
          <div style={{
            transform: `scale(${typeof scale === "number" && !isNaN(scale) ? scale : 1})`,
            transformOrigin: "top center",
          }}>
            {ebook.pages?.map((page, index) => (
              <div
                key={page.id}
                ref={(el) => (pageRefs.current[index] = el)}
                className="relative w-full sm:w-auto mb-5"
                onClick={() => setClickedPageId(page.id)}
              >
                <HtmlContent
                  ebookId={slug}
                  page={page}
                  index={index}
                  width={ebook.width}
                  height={ebook.height}
                />
              </div>
            ))}
          </div>
        </div>
        <div className="fixed top-0 right-0 h-screen w-48 flex flex-col items-center bg-gray-50 border-l hidden lg:flex">
          <div className="flex flex-col items-center mt-8 gap-2 w-full px-4 overflow-y-auto">
            {ebook.pages?.map((page, index) => (
              <div
                key={page.id}
                className={`relative w-full cursor-pointer ${
                  activePage === index ? "border-2 border-blue-500" : ""
                }`}
                style={{
                  backgroundColor: page?.bg_color || "white",
                  height: "200px",
                  margin: "0.25rem",
                  transform: "scale(0.2)",
                  transformOrigin: "top left",
                }}
                onClick={(e) => {
                  e.stopPropagation();
                  setClickedPageId(page.id);
                  pageRefs.current[index]?.scrollIntoView({
                    behavior: "smooth",
                    block: "start",
                  });
                }}
              >
                <HtmlContent
                  ebookId={slug}
                  page={page}
                  index={index}
                  width={ebook.width}
                  height={ebook.height}
                />
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
