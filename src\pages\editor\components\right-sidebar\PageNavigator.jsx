import React from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  setCurrentPage,
  deletePage,
  addPage,
  reorderPages,
} from "../../store/pagesSlice";
import { TrashIcon, GripVerticalIcon, CopyIcon } from "lucide-react";
import api from "@/lib/axios";
import { useLocation, useParams } from "react-router-dom";
import { useQueryClient } from "@tanstack/react-query";

export default function PageNavigator({ handlePageSelect, handleDeletePage: deletePageProp, handleAddPage: addPageProp, syncWithLatestData }) {
  const dispatch = useDispatch();
  const location = useLocation();
  const pages = useSelector((state) => state.pages.present.pages);
  const currentPage = useSelector((state) => state.pages.present.currentPage);
  const { id } = useParams();
  const queryClient = useQueryClient();

  const isTemplateEdit = location.pathname.startsWith("/templates/edit");
  const getIdKey = () => (isTemplateEdit ? "template_id" : "ebook_id");

  const getEndpoint = () => (isTemplateEdit ? "template-pages" : "pages");

  // Use the props if provided, otherwise use the local implementation
  const handleAddPage = async () => {
    if (addPageProp) {
      // Use the prop function from Editor component
      addPageProp();
    } else {
      try {
        const response = await api.post(`admin/${getEndpoint()}`, {
          name: `Page ${pages.length === 0 ? 1 : currentPage + 2}`,
          style: JSON.stringify({
            backgroundColor: "#ffffff",
          }),
          elements: [],
          [getIdKey()]: id,
          order: pages.length === 0 ? 1 : currentPage + 2,
        });

        queryClient.invalidateQueries("ebooksData");
        handlePageSelect(currentPage + 1);

        // Sync with latest data to avoid conflicts during chunk loading
        if (syncWithLatestData) {
          syncWithLatestData();
        }
      } catch (error) {
        console.error("Failed to add page:", error);
      }
    }
  };

  const handleDeletePage = async (pageId) => {
    if (deletePageProp) {
      // Use the prop function from Editor component
      deletePageProp();
    } else {
      try {
        await api.delete(`admin/${getEndpoint()}/${pageId}`);
        dispatch(deletePage(pageId));
        queryClient.invalidateQueries("ebooksData");

        // Sync with latest data to avoid conflicts during chunk loading
        if (syncWithLatestData) {
          syncWithLatestData();
        }
      } catch (error) {
        console.error("Failed to delete page:", error);
      }
    }
  };

  const handleDuplicatePage = async (pageIndex) => {
    try {
      // Get the page to duplicate
      const pageToDuplicate = pages[pageIndex];

      // Create a new page with the same content
      await api.post(`admin/${getEndpoint()}`, {
        name: `Copy of ${pageToDuplicate.name || `Page ${pageIndex + 1}`}`,
        style: typeof pageToDuplicate.style === 'string'
          ? pageToDuplicate.style
          : JSON.stringify(pageToDuplicate.style || { backgroundColor: "#ffffff" }),
        elements: pageToDuplicate.elements || [],
        [getIdKey()]: id,
        order: pageIndex + 2, // Insert after the current page
      });

      // Refresh data and select the new page
      queryClient.invalidateQueries("ebooksData");
      handlePageSelect(pageIndex + 1);

      // Sync with latest data to avoid conflicts during chunk loading
      if (syncWithLatestData) {
        syncWithLatestData();
      }
    } catch (error) {
      console.error("Failed to duplicate page:", error);
    }
  };

  const handleDragStart = (e, index) => {
    e.dataTransfer.setData("text/plain", index);
  };

  const handleDragOver = (e) => {
    e.preventDefault();
  };

  const handleDrop = (e, dropIndex) => {
    e.preventDefault();
    const dragIndex = parseInt(e.dataTransfer.getData("text/plain"), 10);
    if (dragIndex === dropIndex) return;
    const newOrder = [...pages];
    const [draggedPage] = newOrder.splice(dragIndex, 1);
    newOrder.splice(dropIndex, 0, draggedPage);
    dispatch(reorderPages(newOrder));
    const orderedIds = newOrder.map((page) => page.id);
    api
      .put(`/admin/ebooks/${id}/${getEndpoint()}/order`, {
        orderedPageIds: orderedIds,
      })
      .then(() => {
        queryClient.invalidateQueries("ebooksData");

        // Sync with latest data to avoid conflicts during chunk loading
        if (syncWithLatestData) {
          syncWithLatestData();
        }
      });
  };

  return (
    <div className="flex flex-col h-full">
      <div className="flex justify-between items-center mb-2 sticky top-0 bg-white dark:bg-gray-800 py-2">
        <div className="text-sm font-medium text-gray-600 dark:text-white">Pages</div>
        <button
          onClick={handleAddPage}
          className="p-1.5 bg-blue-500 text-white rounded hover:bg-blue-600 text-xs flex items-center"
        >
          <span className="mr-1">+</span> Add Page
        </button>
      </div>

      <div className="overflow-y-auto flex-1 pr-1" style={{ maxHeight: "calc(100vh - 250px)" }}>
        {pages.map((page, index) => (
        <div
          key={page.id}
          draggable
          onDragStart={(e) => handleDragStart(e, index)}
          onDragOver={handleDragOver}
          onDrop={(e) => handleDrop(e, index)}
          className={`flex items-center justify-between p-2 dark:text-white rounded cursor-move
            hover:bg-gray-100 hover:text-black dark:hover:text-black
            ${currentPage === index
              ? "bg-blue-50 text-black dark:text-black"
              : ""
            }`}
          onClick={() => handlePageSelect(index)}
        >
          <div className="flex items-center space-x-2">
            <span className="text-sm">Page {index + 1}</span>
          </div>
          <div className="flex items-center space-x-2 text-gray-400">
            <GripVerticalIcon
              size={16}
              className="text-gray-500"
              style={{ color: "blue" }}
            />
            <CopyIcon
              size={16}
              onClick={(e) => {
                e.stopPropagation();
                handleDuplicatePage(index);
              }}
              className="cursor-pointer"
              style={{ color: "green" }}
            />
            <TrashIcon
              size={16}
              onClick={(e) => {
                e.stopPropagation();
                handleDeletePage(page.id);
              }}
              className="cursor-pointer"
              style={{ color: "red" }}
            />
          </div>
        </div>
      ))}
      {pages.length === 0 && (
        <div className="text-sm text-gray-500 text-center py-4">
          No pages available
        </div>
      )}
      </div>
    </div>
  );
}

