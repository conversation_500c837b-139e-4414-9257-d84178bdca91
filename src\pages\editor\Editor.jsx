// Editor.jsx

import { useRef, useEffect } from "react";
import { Icon } from "@iconify/react";
import Topbar from "../editor/components/Topbar";
import Sidebar from "../editor/components/LeftSidebar";
import RightSidebar from "../editor/components/RightSidebar";
import Page from "../editor/components/Page";
import EmptyState from './components/EmptyState';
import { useDispatch, useSelector } from "react-redux";
import { setCurrentPage, setPages, setScale } from "../editor/store/pagesSlice";
import { useEbook } from "../book-preview/hooks/useEbook";
import { useParams } from "react-router-dom";
import api from "@/lib/axios";
import { persistor } from "@/store/store";
import { ActionCreators } from 'redux-undo';
import { v4 as uuidv4 } from 'uuid';
// No longer need useQueryClient since we're not invalidating queries
import { setWidth } from "../book-preview/store/ebookSlice";

export default function Editor() {
  const dispatch = useDispatch();

  // Selectors accessing `present` state
  const pages = useSelector((state) => state.pages.present.pages);
  const currentPage = useSelector((state) => state.pages.present.currentPage);
  const scale = useSelector((state) => state.pages.present.scale);
  const isPreview = useSelector((state) => state.toolbar.isPreviewActive); // Assuming toolbar is another slice
  const pageRefs = useRef([]);

  // Fetch eBooks data
  const { id } = useParams();
  const {
    ebook: ebooksData,
    isLoading: ebooksLoading,
    isLoadingMore,
    loadingProgress
  } = useEbook();

  // Initialize pages from fetched data
  useEffect(() => {
    console.log("Editor received ebooksData:", ebooksData);

    // Determine where the pages data is located
    let pagesData;
    let ebookWidth;

    if (ebooksData?.data?.pages) {
      // Structure: ebooksData.data.pages
      pagesData = ebooksData.data.pages;
      ebookWidth = ebooksData.data.width;
      console.log("Found pages in ebooksData.data.pages");
    } else if (ebooksData?.pages) {
      // Structure: ebooksData.pages
      pagesData = ebooksData.pages;
      ebookWidth = ebooksData.width;
      console.log("Found pages in ebooksData.pages");
    } else {
      console.warn("No pages data found in ebooksData:", ebooksData);
      return;
    }

    console.log("Pages data structure:", pagesData);
    console.log("Pages count:", pagesData.length);

    dispatch(setWidth(ebookWidth));
    const formattedPages = pagesData.map((page, pageIndex) => {
      console.log(`Processing page ${pageIndex}:`, page);
      return {
        ...page,
        id: page.id || uuidv4(), // Ensure each page has an ID
        elements: page.elements || [],
        style: typeof page.style === 'string' && page.style.startsWith('{') ? JSON.parse(page.style) : page.style || {},
        name: page.name || `Page ${pageIndex + 1}`, // Optional: Add a default name if not present
      };
    });

    console.log("Formatted pages:", formattedPages);
    dispatch(setPages(formattedPages));
    console.log(`Initialized ${formattedPages.length} pages in the editor`);
  }, [ebooksData, dispatch]);


  // Create a ref to track the last updated page content
  const lastPageContentRef = useRef({});
  const updateTimeoutRef = useRef(null);

  // Auto-update page ONLY when elements or style actually change - with debouncing
  useEffect(() => {
    // Skip during initial load or when no pages exist
    if (!pages.length || currentPage < 0 || currentPage >= pages.length) return;

    const currentPageData = pages[currentPage];
    if (!currentPageData || !currentPageData.id) return;

    // Get the current page ID to track which page we're working with
    const pageId = currentPageData.id;

    // Create a content signature that only includes the parts we care about for detecting changes
    // This excludes metadata that might change during navigation but doesn't require a PUT
    const contentSignature = {
      elements: JSON.stringify(currentPageData.elements || []),
      style: JSON.stringify(currentPageData.style || {})
    };

    // Check if this is the first time seeing this page
    if (!lastPageContentRef.current[pageId]) {
      // Initialize tracking for this page without triggering a PUT
      lastPageContentRef.current[pageId] = contentSignature;
      return;
    }

    // Compare current content with last saved content for this specific page
    const lastContent = lastPageContentRef.current[pageId];
    const hasContentChanged =
      lastContent.elements !== contentSignature.elements ||
      lastContent.style !== contentSignature.style;

    // Only proceed with update if content actually changed
    if (hasContentChanged) {
      // Update our reference to the latest content
      lastPageContentRef.current[pageId] = contentSignature;

      // Clear any pending update for previous changes
      if (updateTimeoutRef.current) {
        clearTimeout(updateTimeoutRef.current);
      }

      // Schedule the update with debouncing
      updateTimeoutRef.current = setTimeout(() => {
        const pageToUpdate = {
          ...currentPageData,
          style: typeof currentPageData.style === 'string'
            ? currentPageData.style
            : JSON.stringify(currentPageData.style)
        };

        console.log(`Updating page ${pageId} with debouncing - content actually changed`);
        api.put(`admin/pages/${pageId}`, pageToUpdate, { showToast: false });
        updateTimeoutRef.current = null;
      }, 2000); // 2 second debounce
    }

    return () => {
      // Clean up timeout if component unmounts or dependencies change
      if (updateTimeoutRef.current) {
        clearTimeout(updateTimeoutRef.current);
      }
    };
  }, [pages, currentPage]);

  // Handle Page Selection
  const handlePageSelect = (index) => {
    dispatch(setCurrentPage(index));
    if (pageRefs.current[index]) {
      pageRefs.current[index].scrollIntoView({ behavior: 'smooth' });
    }
  };

  // Handle Undo and Redo
  useEffect(() => {
    const handleKeyDown = (e) => {
      // For Windows/Linux and Mac
      const isCtrlOrCmd = e.ctrlKey || e.metaKey;

      if (isCtrlOrCmd && e.key === 'z') {
        e.preventDefault();
        dispatch(ActionCreators.undo());
      }
      if (isCtrlOrCmd && (e.key === 'y' || (isCtrlOrCmd && e.shiftKey && e.key === 'z'))) {
        e.preventDefault();
        dispatch(ActionCreators.redo());
      }
    };

    window.addEventListener('keydown', handleKeyDown);

    // Cleanup on unmount
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [dispatch]);

  // Show loading screen if initial data is not loaded yet
  if (ebooksLoading) {
    return (
      <div className="flex flex-col items-center justify-center h-screen bg-gray-50">
        <div className="text-center mb-4 text-xl font-medium text-gray-700">Loading pages...</div>
        <div className="w-80 bg-gray-200 rounded-full h-3 shadow-inner">
          <div
            className="bg-blue-600 h-3 rounded-full transition-all duration-300 shadow"
            style={{ width: `${loadingProgress || 10}%` }}
          ></div>
        </div>
      </div>
    );
  }


  const isTemplateEdit = location.pathname.startsWith("/templates/edit");
  const getIdKey = () => (isTemplateEdit ? "template_id" : "ebook_id");

  const getEndpoint = () => (isTemplateEdit ? "template-pages" : "pages");

  const handleAddPage = async () => {
    try {
      // Create new page data
      const newPageData = {
        name: "New Page",
        style: JSON.stringify({
          backgroundColor: "#ffffff",
        }),
        elements: [],
        [getIdKey()]: id,
        order: pages[currentPage]?.order || 1,
      };

      // Send API request to create the page
      const response = await api.post(`admin/${getEndpoint()}`, newPageData);

      // Get the newly created page from the response
      const newPage = response.data.data;

      // Format the new page to match the expected structure
      const formattedNewPage = {
        ...newPage,
        id: newPage.id || uuidv4(),
        elements: newPage.elements || [],
        style: typeof newPage.style === 'string' && newPage.style.startsWith('{')
          ? JSON.parse(newPage.style)
          : newPage.style || {},
        name: newPage.name || `Page ${pages.length + 1}`,
      };

      // Create a new pages array with the new page inserted after the current page
      const updatedPages = [...pages];
      updatedPages.splice(currentPage + 1, 0, formattedNewPage);

      // Update the Redux store with the new pages array
      dispatch(setPages(updatedPages));

      // Select the newly added page
      dispatch(setCurrentPage(currentPage + 1));

      console.log(`Added new page at index ${currentPage + 1}`);
    } catch (error) {
      console.error("Failed to add page:", error);
    }
  };

  const handleDeletePage = async () => {
    // Don't delete if there's only one page
    if (pages.length <= 1) {
      console.warn("Cannot delete the only page");
      return;
    }

    try {
      const pageToDelete = pages[currentPage];

      // Send API request to delete the page
      await api.delete(`admin/${getEndpoint()}/${pageToDelete.id}`);

      // Create a new pages array without the deleted page
      const updatedPages = [...pages];
      updatedPages.splice(currentPage, 1);

      // Update the Redux store with the new pages array
      dispatch(setPages(updatedPages));

      // Select the previous page or the first page if we deleted the first page
      const newPageIndex = currentPage > 0 ? currentPage - 1 : 0;
      dispatch(setCurrentPage(newPageIndex));

      console.log(`Deleted page at index ${currentPage}, now at index ${newPageIndex}`);
    } catch (error) {
      console.error("Failed to delete page:", error);
    }
  };

  // Get the ebook dimensions for rendering
  const getEbookDimensions = () => {
    if (ebooksData?.data?.width && ebooksData?.data?.height) {
      return {
        width: ebooksData.data.width,
        height: ebooksData.data.height
      };
    } else if (ebooksData?.width && ebooksData?.height) {
      return {
        width: ebooksData.width,
        height: ebooksData.height
      };
    } else {
      console.warn("No ebook dimensions found, using defaults");
      return {
        width: 800,
        height: 1200
      };
    }
  };

  const ebookDimensions = getEbookDimensions();

  return (
    <div className="flex flex-col h-screen bg-gray-100 dark:bg-gray-900">
      {/* Topbar */}
      <Topbar />

      {/* Background loading indicator */}
      {isLoadingMore && (
        <div className="fixed top-0 left-0 right-0 z-50">
          <div className="h-1 bg-gray-200">
            <div
              className="h-1 bg-blue-600 transition-all duration-300"
              style={{ width: `${loadingProgress}%` }}
            ></div>
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className="flex flex-grow overflow-hidden">
        {/* Left Sidebar */}
        {!isPreview && <Sidebar />}

        {/* Pages Container */}
        <div className="flex-1 relative overflow-auto" style={{ scrollbarWidth: 'thin' }}>
          <div
            className="flex flex-col items-center"
            style={{
              transform: `scale(${scale})`,
              transformOrigin: "top center",
              display: "flex",
              alignItems: "center",
            }}
          >
            {pages.length === 0 && (
              <EmptyState handleAddPage={handleAddPage} bookId={id} />
            )}
            {pages.map((page, index) => (
              <div
                key={page.id}
                ref={(el) => (pageRefs.current[index] = el)}
                className={`m-6 mb-4 ${index === currentPage
                  ? "border border-blue-500 shadow-md"
                  : "shadow-lg"
                  } cursor-pointer`}
                onClick={() => dispatch(setCurrentPage(index))}
              >


                <Page
                  key={page.id}
                  page={page}
                  index={index}
                  height={ebookDimensions.height}
                  width={ebookDimensions.width}
                />
              </div>
            ))}
          </div>
        </div>

        {/* Right Sidebar */}
        {!isPreview && <RightSidebar
          handlePageSelect={handlePageSelect}
          handleDeletePage={handleDeletePage}
          handleAddPage={handleAddPage}
          syncWithLatestData={() => console.log("Data already in sync")}
        />}
      </div>

      {/* Scale Controller
      <div className="absolute bottom-4 right-4 flex items-center space-x-2 bg-white p-2 rounded shadow">
        <Icon icon="mdi:magnify" />
        <input
          type="range"
          min="0.5"
          max="2"
          step="0.1"
          value={scale}
          onChange={(e) => dispatch(setScale(parseFloat(e.target.value)))}
          className="w-48"
        />
        <span>{(scale * 100).toFixed(0)}%</span>
      </div> */}
    </div>
  );
}
