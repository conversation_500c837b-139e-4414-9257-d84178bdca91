import React from "react";
import { useParams } from "react-router-dom";
import TemplateCard from "@/components/ui/TemplateCard";
import Confirm from "@/components/ui/Confirm";
import EbookCardSkeleton from "@/components/ui/EbookCardSkeleton";
import useDataFetching from "@/hooks/useDataFetching";
import api from "@/lib/axios";
import { useQueryClient } from '@tanstack/react-query';

const Templates = () => {
  const { id } = useParams();
  const queryClient = useQueryClient();

  const { data: books, isLoading, refetch } = useDataFetching({
    queryKey: ["templates"],
    endPoint: "/admin/templates",
  });

  const handleUse = (book) => {
    Confirm(
      async () => {
        try {
          await api.post(`admin/ebooks/use-templates`, { ebook_id: id, template_id: book.id });
          queryClient.invalidateQueries('ebooksData');
          refetch();
        } catch (error) {
          console.error('Error using template:', error);
        }
      },
      "Are you sure you want to use this template?",
      "This template",
      "will replace your current book page data.",
      "Cancel",
      "Use Template" // Changed from "Delete" to "Use Template"
    );
  };

  return (
    <div className="min-h-screen">
      <h2 className="text-2xl font-bold mb-4">Templates</h2>
      <div className="grid grid-cols-1 sm:grid-cols-1 gap-6">
        {isLoading
          ? Array.from({ length: 5 }, (_, index) => (
            <EbookCardSkeleton key={index} />
          ))
          : books?.data?.data?.length > 0 ? (
            <div className="max-h-[calc(100vh-200px)] overflow-y-auto">
              {books?.data?.data?.map((book) => (
                <TemplateCard
                  key={book.id}
                  book={book}
                  onUse={() => handleUse(book)}
                />
              ))}
            </div>
          ) : (
            <div className="col-span-full text-center text-gray-500 dark:text-gray-400">
              No templates found.
            </div>
          )}
      </div>
    </div>
  );
};

export default Templates;
